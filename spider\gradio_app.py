import gradio as gr
import requests
import os
import time
import json
import asyncio
import sqlite3
import traceback
import pymysql
from datetime import datetime
from urllib.parse import urlparse
from google import genai
from google.genai import types
from moviepy import VideoFileClip
import io
import tempfile
import mimetypes
from image_compatibility import process_image_to_jpeg_stream

from config import *
from logger import logger
from async_downloader import downloader

class BaseProcessor:
    """基础处理器类，包含通用的配置和方法"""

    def __init__(self, environment):
        """初始化处理器

        Args:
            environment: 环境名称 ('test' 或 'online')
        """
        if environment not in ENVIRONMENTS:
            raise ValueError(f"不支持的环境: {environment}")

        self.environment = environment
        self.env_config = ENVIRONMENTS[environment]
        self.strapi_url = self.env_config["strapi_url"]
        self.token = self.env_config["token"]
        self.db_config = self.env_config["database"]

        # AI配置
        self.gemini_api_key = GEMINI_API_KEYS.get(environment)

    def get_mysql_connection(self):
        """获取MySQL连接"""
        try:
            connection = pymysql.connect(
                host=self.db_config["host"],
                port=int(self.db_config["port"]),
                user=self.db_config["username"],
                password=self.db_config["password"],
                database=self.db_config["name"],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            logger.info(f"连接到{self.environment}环境数据库成功")
            return connection, "数据库连接成功"
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return None, f"数据库连接失败: {str(e)}"

    def get_strapi_headers(self, content_type="application/json"):
        """获取Strapi请求头"""
        headers = {"Authorization": f"Bearer {self.token}"}
        if content_type:
            headers["Content-Type"] = content_type
        return headers

    def append_to_output(self, new_text):
        """添加输出文本到日志"""
        logger.info(new_text)
        return new_text

class DataSyncProcessor(BaseProcessor):
    """数据同步处理器"""

    def upload_image(self, file_path, eid=None, field=None, table="api::episode.episode"):
        """上传图片到Strapi"""
        try:
            file_name, file_content = process_image_to_jpeg_stream(file_path)
            mime_type, _ = mimetypes.guess_type(file_name)

            # 检查图片是否已存在
            image_data = self.get_image(file_name)
            if len(image_data) > 0:
                msg = f"Found Image {file_name} in strapi cms backend."
                return image_data, msg

            if not mime_type:
                mime_type = 'application/octet-stream'

            files = {'files': (file_name, file_content, mime_type)}
            headers = {"Authorization": f"Bearer {self.token}"}

            data = {}
            if eid:
                data = {"ref": table, "refId": eid, "field": field}

            response = requests.post(f'{self.strapi_url}/api/upload',
                                   headers=headers, files=files, data=data)

            if response.ok:
                logger.info(f"图片上传成功: {file_name}")
                return response.json(), f"图片上传成功: {file_name}"
            else:
                logger.error(f"图片上传失败: {response.status_code}")
                return None, f"图片上传失败: {response.status_code}"

        except Exception as e:
            logger.error(f"上传图片异常: {str(e)}")
            return None, f"上传图片异常: {str(e)}"

    def get_image(self, file_name):
        """检查图片是否已存在"""
        try:
            headers = self.get_strapi_headers()
            response = requests.get(f"{self.strapi_url}/api/upload/files",
                                  headers=headers, params={"filters[name][$eq]": file_name})
            if response.ok:
                data = response.json()
                return data if data else []
            return []
        except Exception as e:
            logger.error(f"检查图片失败: {str(e)}")
            return []

    def find_episodes(self, data):
        """查找剧集"""
        try:
            headers = self.get_strapi_headers()
            params = {"filters[playlet_id][$eq]": data["playlet_id"]}
            response = requests.get(f"{self.strapi_url}/api/episodes",
                                  headers=headers, params=params)

            if response.ok:
                result = response.json()
                msg = f"Successfully found episodes for playletId {data['playlet_id']}"
                return result, msg
            else:
                msg = f"Error finding episodes for playletId {data['playlet_id']}: {response.status_code}"
                return None, msg

        except Exception as e:
            msg = f"Error finding episodes: {traceback.format_exc()}"
            return None, msg

    def create_episodes(self, data):
        """创建剧集"""
        try:
            strapi_payload = {"data": data}
            headers = self.get_strapi_headers()
            response = requests.post(f"{self.strapi_url}/api/episodes",
                                   headers=headers, json=strapi_payload)
            response.raise_for_status()
            msg = f"Successfully created episodes for playletId {data['playlet_id']}"
            return response.json(), msg

        except Exception as e:
            msg = f"Error creating episodes for playletId {data['playlet_id']}: {traceback.format_exc()}"
            return None, msg

    def update_episodes(self, document_id, data):
        """更新剧集"""
        try:
            strapi_payload = {"data": data}
            headers = self.get_strapi_headers()
            response = requests.put(f"{self.strapi_url}/api/episodes/{document_id}",
                                  headers=headers, json=strapi_payload)
            response.raise_for_status()
            msg = f"Successfully updated episodes for documentId {document_id}"
            return response.json(), msg

        except Exception as e:
            msg = f"Error updating episodes for {document_id}: {traceback.format_exc()}"
            return None, msg

class DatabaseManager:
    """数据库管理器类，封装SQLite3操作"""

    def __init__(self, db_name=None):
        """初始化数据库管理器

        Args:
            db_name: 数据库文件名，默认使用配置中的DATABASE_NAME
        """
        self.db_name = db_name or DATABASE_NAME
        self.create_database()

    def get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_name)

    def create_database(self):
        """创建数据库和表"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS entity_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entity_name TEXT UNIQUE,
                entity_path TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS drama_summary (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                playlet_id TEXT,
                material_id TEXT UNIQUE,
                ad_title TEXT,
                video_url TEXT,
                summary TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        conn.commit()
        conn.close()

    def check_entity_exists(self, entity_name):
        """检查实体是否已存在"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(
            "SELECT entity_path FROM entity_data WHERE entity_name = ? ",
            (entity_name)
        )
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None

    def save_entity_data(self, entity_name, entity_path):
        """保存实体数据"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(
                "INSERT OR REPLACE INTO entity_data (entity_name, entity_path) VALUES (?, ?)",
                (entity_name, entity_path)
            )
            conn.commit()
            logger.info(f"保存实体数据: {entity_name} -> {entity_path}")
            return True
        except Exception as e:
            logger.error(f"保存实体数据失败: {str(e)}")
            return False
        finally:
            conn.close()

    def check_summary_exists(self, material_id):
        """检查摘要是否已存在"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT summary FROM drama_summary WHERE material_id = ?", (material_id,))
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None

    def save_summary_data(self, playlet_id, material_id, ad_title, video_url, summary):
        """保存摘要数据"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(
                "INSERT OR REPLACE INTO drama_summary (playlet_id, material_id, ad_title, video_url, summary) VALUES (?, ?, ?, ?, ?)",
                (playlet_id, material_id, ad_title, video_url, summary)
            )
            conn.commit()
            logger.info(f"保存摘要数据: {material_id}")
            return True
        except Exception as e:
            logger.error(f"保存摘要数据失败: {str(e)}")
            return False
        finally:
            conn.close()

# 全局数据库管理器实例
db_manager = DatabaseManager()

class VideoSummaryProcessor(BaseProcessor):
    """视频摘要处理器"""

    def __init__(self, environment):
        super().__init__(environment)
        self.data_sync = DataSyncProcessor(environment)

    def get_cached_summary(self, material_id):
        """从缓存获取摘要"""
        return db_manager.check_summary_exists(material_id)

    def save_summary(self, playlet_id, material_id, ad_title, video_url, summary):
        """保存摘要到缓存"""
        return db_manager.save_summary_data(playlet_id, material_id, ad_title, video_url, summary)

    def extract_first_five_minutes_to_buffer(self, input_path):
        """提取视频前5分钟到缓冲区"""
        try:
            video_clip = VideoFileClip(input_path)
            duration_to_extract = min(video_clip.duration, VIDEO_EXTRACT_DURATION)
            subclip = video_clip.subclipped(0, duration_to_extract)

            with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as tmp_file:
                temp_file_path = tmp_file.name

            subclip.write_videofile(
                temp_file_path,
                codec="libx264",
                audio_codec="aac",
                logger=None
            )

            with open(temp_file_path, "rb") as f:
                video_buffer = io.BytesIO(f.read())

            os.remove(temp_file_path)
            video_clip.close()
            subclip.close()
            video_buffer.seek(0)

            msg = f"Extracted first {VIDEO_EXTRACT_DURATION//60} minutes of video {input_path} to buffer."
            return video_buffer, msg

        except Exception as e:
            msg = f"Error extracting video {input_path}: {traceback.format_exc()}"
            return None, msg

    def realize_video(self, video_path):
        """使用AI分析视频并生成摘要"""
        summary = ""
        msg = ""

        try:
            client = genai.Client(api_key=self.gemini_api_key)
            video_data, extract_msg = self.extract_first_five_minutes_to_buffer(video_path)
            if video_data is None:
                return summary, extract_msg

            # 根据环境选择不同的处理方式
            if self.environment == "online":
                # 线上环境使用内联数据
                content = types.Content(
                    parts=[
                        types.Part(
                            inline_data=types.Blob(data=video_data, mime_type='video/mp4')
                        ),
                        types.Part(text=f"请将上述视频故事重写为一篇叙事的小说片段，突出故事的剧情冲突和吸引读者继续读下去的特点。限制在{SUMMARY_MAX_LENGTH}字之内。")
                    ]
                )
                response = client.models.generate_content(
                    model="gemini-2.0-flash", contents=content
                )
            else:
                # 测试环境上传文件
                video_file = client.files.upload(file=video_data, config={"mime_type": "video/mp4"})
                video_file = client.files.get(name=video_file.name)
                while video_file.state != "ACTIVE":
                    time.sleep(10)
                    video_file = client.files.get(name=video_file.name)

                logger.info(f"{video_file.name} is uploaded and active. start to generate content.")
                response = client.models.generate_content(
                    model="gemini-2.0-flash",
                    contents=[video_file, f"请将上述视频故事重写为一篇叙事的小说片段，突出故事的剧情冲突和吸引读者继续读下去的特点。限制在{SUMMARY_MAX_LENGTH}字之内。"]
                )

            summary = response.text

            # 使用小说样本重写内容
            summary = self.rewrite_with_novel_samples(client, summary)
            msg = f"Successfully summarized video {video_path}"
            return summary, msg

        except Exception as e:
            msg = f"Error processing video {video_path}: {traceback.format_exc()}"
            return summary, msg

    def rewrite_with_novel_samples(self, client, summary):
        """使用小说样本重写内容"""
        try:
            novel_sample = ""
            with open("data/novel.json", "r", encoding="utf-8") as f:
                novel = json.load(f)
                for title, item in novel.items():
                    novel_sample += f"{title}\n{item['content']}\n\n"

            logger.info("Start to rewrite content with novel samples.")
            response = client.models.generate_content(
                model="gemini-2.0-flash",
                contents=[f"以下是目前较受欢迎的小说写法:\n{novel_sample}, 以下是一段剧情：{summary}, 从流行小说片段中挑选合适的写法,重写这一段剧情，以达到更好的戏剧化效果。直接输出改写后的内容，不要输出具体的分析细节。"]
            )
            return response.text
        except Exception as e:
            logger.error(f"Error rewriting with novel samples: {str(e)}")
            return summary  # 如果重写失败，返回原始摘要

def read_headers_from_text(text: str):
    """从文本读取请求头"""
    headers = {}
    for line in text.split("\n"):
        line = line.strip()
        if line and ':' in line:
            key, value = line.split(':', 1)
            headers[key.strip()] = value.strip()
    return headers

def fetch_api_data(url, headers, params=None):
    """获取API数据"""
    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        logger.info(f"API请求成功: {url}")
        return response.json()
    except Exception as e:
        logger.error(f"API请求失败: {url} - {str(e)}")
        return None

def crawl_hot(day, intersection, progress=gr.Progress()):
    """爬取热力榜数据"""
    logger.info("开始爬取热力榜数据")
    progress(0, desc="初始化...")
    
    headers = read_headers_from_text(intersection)
    if not headers:
        return "错误: 无效的请求头"
    
    params = {"pageId": "1", "pageSize": "10", "day": day}
    
    progress(0.2, desc="获取数据...")
    page_data = {}
    if type(API_URLS["hot_ranking"]) is list:
        for url in API_URLS["hot_ranking"]:
            temp_data = fetch_api_data(url, headers, params)
            if temp_data:
                for key, value in temp_data.items():
                    if key in page_data and type(page_data[key]) is list:
                        page_data[key].extend(value)
                    else:
                        page_data[key] = value
    else:
        page_data = fetch_api_data(API_URLS["hot_ranking"], headers, params)

    if not page_data:
        return "API请求失败，请检查网络连接和请求头"

    # 准备下载任务
    download_tasks = []
    image_save_dir = f"./data/{day.replace('-', '/')}/covers"
    os.makedirs(image_save_dir, exist_ok=True)
    
    progress(0.4, desc="准备下载任务...")
    for item in page_data.get("content", []):
        if isinstance(item, dict):
            playlet_id = item.get("playletId", "")
            image_url = item.get("coverOss", "") or item.get("cover", "")
            if image_url and playlet_id:
                filename = f"{playlet_id}_cover.jpg"
                filepath = os.path.join(image_save_dir, filename)
                download_tasks.append((image_url, filepath, playlet_id))
    
    # 异步下载
    if download_tasks:
        progress(0.6, desc="下载图片...")
        
        def download_progress(completed, total):
            progress(0.6 + 0.3 * (completed / total), 
                    desc=f"下载进度: {completed}/{total}")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        _ = loop.run_until_complete(
            downloader.download_batch(download_tasks, download_progress)
        )
        loop.close()
    
    # 保存数据
    progress(0.9, desc="保存数据...")
    data_save_dir = f"./data/{day.replace('-', '/')}"
    os.makedirs(data_save_dir, exist_ok=True)
    
    with open(f"{data_save_dir}/data.json", "w", encoding="utf-8") as f:
        json.dump(page_data, f, ensure_ascii=False, indent=4)
    
    progress(1.0, desc="完成")
    logger.info("热力榜数据爬取完成")
    return f"热力榜数据爬取完成，保存到: {data_save_dir}/data.json"

def download_media_files(idx, total_media, playlet_id, day, detail_data, progress_callback=None):
    """下载媒体文件（图片和视频）"""
    base_dir = f"./data/{day.replace('-', '/')}/{playlet_id}"
    images_dir = f"{base_dir}/images"
    videos_dir = f"{base_dir}/videos"

    os.makedirs(images_dir, exist_ok=True)
    os.makedirs(videos_dir, exist_ok=True)

    download_tasks = []
    downloaded_video = True
    # 处理物料列表中的视频和图片

    if "statusCode" in detail_data and detail_data["statusCode"] != 200:
        logger.logger(f"登录信息失效，请重新登录。")
        return 0
        
    material_list = detail_data.get("content", {}).get("materialList", [])
    for i, material in enumerate(material_list):
        material_id = material.get("materialId", f"material_{i}")

        # 处理视频 只下载第一个存在的视频
        video_url = material.get("videoUrl", "")
        if video_url and video_url.startswith("http") and downloaded_video:
            video_name = f"{material_id}.mp4"
            existing_path = db_manager.check_entity_exists(video_name)
            if not existing_path:
                video_path = os.path.join(videos_dir, video_name)
                download_tasks.append((video_url, video_path, playlet_id, "video", video_name))
            downloaded_video = False

        # 处理图片
        image_url = material.get("cover", "")
        if image_url:
            image_name = f"{material_id}.jpg"
            existing_path = db_manager.check_entity_exists(image_name)
            if not existing_path:
                image_path = os.path.join(images_dir, image_name)
                download_tasks.append((image_url, image_path, playlet_id, "image", image_name))

    # 执行下载
    if download_tasks:
        logger.info(f"开始下载 {len(download_tasks)} 个媒体文件")

        def download_progress(completed, total):
            if progress_callback:
                progress_callback(idx, total_media, completed, total, f"下载媒体文件: {completed}/{total}")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # 准备异步下载任务
        async_tasks = [(url, path, playlet_id) for url, path, playlet_id, _, _ in download_tasks]
        results = loop.run_until_complete(
            downloader.download_batch(async_tasks, download_progress)
        )
        loop.close()

        # 保存下载记录到数据库
        for (_, path, playlet_id, entity_type, entity_name), success in zip(download_tasks, results):
            if success:
                db_manager.save_entity_data(entity_name, path, entity_type, playlet_id)

        logger.info(f"媒体文件下载完成，成功: {len(results)}/{len(download_tasks)}")
        return len(results)

    return 0

def crawl_detail_pages(day, intersection, progress=gr.Progress()):
    """爬取详情页数据"""
    logger.info("开始爬取详情页数据")
    progress(0, desc="初始化...")

    headers = read_headers_from_text(intersection)
    if not headers:
        return "错误: 无效的请求头"

    # 读取热力榜数据
    data_path = f"./data/{day.replace('-', '/')}/data.json"
    try:
        with open(data_path, encoding="utf-8") as f:
            day_data = json.load(f)
    except FileNotFoundError:
        return f"错误: 请先爬取热力榜数据 - {data_path}"

    total = len(day_data.get("content", []))
    if total == 0:
        return "错误: 没有找到需要爬取的剧集数据"

    processed = 0
    downloaded_files = 0
    data_save_dir = f"./data/{day.replace('-', '/')}"

    for i, item in enumerate(day_data.get("content", [])):
        playlet_id = item.get("playletId", "")
        if not playlet_id:
            continue

        progress(i / total * 0.5, desc=f"爬取详情页: {playlet_id}")

        try:
            # 爬取剧集详情
            detail_url = API_URLS["playlet_info"].format(playletId=playlet_id)
            detail_data = fetch_api_data(detail_url, headers)
            maker_team_url = API_URLS["making_team"].format(playletId=playlet_id)
            maker_team_data = fetch_api_data(maker_team_url, headers)

            content_data = {}
            content_data["getPlayletInfo"] = detail_data
            content_data["makingTeam"] = maker_team_data

            # 保存详情数据
            content_file = f"{data_save_dir}/{playlet_id}.json"
            with open(content_file, "w", encoding="utf-8") as f:
                json.dump(content_data, f, ensure_ascii=False, indent=4)
            logger.info(f"详情页数据保存成功: {content_file}")

            if detail_data:
                # 下载媒体文件
                def media_progress(idx, total, prog, total_task, desc):
                    progress(idx / total * 0.5 + prog * 0.5 / total_task, desc=f"下载媒体文件: {desc}")

                files_downloaded = download_media_files(i, total,playlet_id, day, detail_data, media_progress)
                logger.info(f"媒体文件下载完成: {files_downloaded}")
                downloaded_files += files_downloaded
                processed += 1
            else:
                logger.error(f"详情页数据获取失败: {playlet_id}")

        except Exception as e:
            logger.error(f"处理详情页失败: {playlet_id} - {traceback.format_exc()}")

    progress(1.0, desc="完成")
    logger.info("详情页数据爬取完成")
    return f"详情页数据爬取完成！总计: {total}, 成功处理: {processed}, 下载文件: {downloaded_files}"

def sync_to_environment(day, environment, progress=gr.Progress()):
    """同步数据到指定环境"""
    logger.info(f"开始同步数据到{environment}环境")
    progress(0, desc="初始化同步器...")

    try:
        processor = DataSyncProcessor(environment)

        # 加载数据文件
        data_path = f"./data/{day.replace('-', '/')}/data.json"
        try:
            with open(data_path, encoding="utf-8") as f:
                day_data = json.load(f)
        except FileNotFoundError:
            error_msg = f"数据文件不存在: {data_path}"
            logger.error(error_msg)
            return error_msg

        total = len(day_data["content"])
        processed = 0

        for i, item in enumerate(day_data["content"]):
            playlet_id = item["playletId"]
            progress(i / total * 0.9, desc=f"处理 {playlet_id}...")

            processor.append_to_output(f"Start Processing playlet_id: {playlet_id}")

            # 加载详细信息
            playlet_info_path = f"./data/{day.replace('-', '/')}/{playlet_id}.json"
            try:
                with open(playlet_info_path, encoding="utf-8") as f:
                    playlet_info = json.load(f)
            except FileNotFoundError:
                processor.append_to_output(f"Error: {playlet_id}.json not found for playletId {playlet_id}")
                continue

            # 处理剧集数据
            try:
                playlet_name = playlet_info["getPlayletInfo"]["playletName"]
                description = playlet_info["getPlayletInfo"].get("description", "")
                publish_time = playlet_info["getPlayletInfo"].get("releaseStartDate", "")
                playlet_tags = playlet_info["getPlayletInfo"].get("playletTags", [])

                data = {
                    "playlet_id": str(playlet_id),
                    "publish_time": publish_time,
                    "name": playlet_name,
                    "description": description,
                    "keywords": playlet_tags,
                    "category": playlet_tags[0] if playlet_tags else "未知"
                }

                # 查找或创建剧集
                episodes, msg = processor.find_episodes(data)
                processor.append_to_output(msg)

                if episodes and len(episodes["data"]) == 0:
                    episodes, msg = processor.create_episodes(data)
                    processor.append_to_output(msg)
                    episodes, msg = processor.find_episodes(data)
                    processor.append_to_output(msg)

                if not episodes or "data" not in episodes or len(episodes["data"]) == 0:
                    processor.append_to_output(f"No episodes found for playletId {playlet_id}, skipping...")
                    continue

                document_id = episodes["data"][0]["documentId"]
                episodes, msg = processor.update_episodes(document_id, data)
                processor.append_to_output(msg)

                # 处理封面上传
                cover_oss_local_path = item.get("cover_oss")
                if cover_oss_local_path:
                    eid = episodes["data"][0]["id"]
                    processor.append_to_output(f"Attempting to upload cover: {cover_oss_local_path}")
                    _, msg = processor.upload_image(cover_oss_local_path, eid, "cover")
                    processor.append_to_output(msg)

                processor.append_to_output(f"Processing data for playletId {playlet_id} has finished.")
                processed += 1

            except Exception as e:
                processor.append_to_output(f"Error processing data for playletId {playlet_id}: {traceback.format_exc()}")

        progress(1.0, desc="同步完成")
        processor.append_to_output(f"Sync finished. Total: {total}, Processed: {processed}")
        logger.info(f"{environment}环境同步完成")
        return f"同步完成！总计: {total}, 成功处理: {processed}"

    except Exception as e:
        error_msg = f"同步失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

def crawl_from_zhihu():
    """从知乎爬取小说样本"""
    tab_types = ["hot", "recommend", "like", "read"]
    channel_types = ["male", "female"]

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
    }

    novel = {}
    for tab_type in tab_types:
        for channel_type in channel_types:
            r_url = f"https://www.zhihu.com/api/vip/km-indep-home-comm/billboard/list?channel_type={channel_type}&filter_key=0&limit=10&offset=0&tab_type={tab_type}"

            try:
                response = requests.get(r_url, headers=headers)
                response.raise_for_status()
                data = response.json()
                for item in data["data"]:
                    if item["title"] not in novel:
                        novel[item["title"]] = {
                            "title": item["title"],
                            "content": item["content_abstract"],
                            "artwork": item["artwork"],
                            "url": item["url"],
                            "labels": item["labels"]
                        }
            except Exception as e:
                logger.error(f"爬取知乎数据失败: {str(e)}")

    with open("data/novel.json", "w", encoding="utf-8") as f:
        json.dump(novel, f, ensure_ascii=False, indent=4)

def summary_videos(day, environment, progress=gr.Progress()):
    """AI解析视频摘要"""
    logger.info(f"开始{environment}环境视频AI解析")
    progress(0, desc="初始化AI处理器...")

    try:
        # 先爬取知乎小说样本
        crawl_from_zhihu()

        processor = VideoSummaryProcessor(environment)

        # 加载数据文件
        data_path = f"./data/{day.replace('-', '/')}/data.json"
        try:
            with open(data_path, encoding="utf-8") as f:
                day_data = json.load(f)
        except FileNotFoundError:
            error_msg = f"数据文件不存在: {data_path}"
            logger.error(error_msg)
            return error_msg

        total = len(day_data["content"])
        processed = 0

        for i, item in enumerate(day_data["content"]):
            playlet_id = item["playletId"]
            progress(i / total * 0.9, desc=f"处理 {playlet_id}...")

            processor.append_to_output(f"Processing playletId: {playlet_id}")

            # 加载详细信息
            playlet_info_path = f"./data/{day.replace('-', '/')}/{playlet_id}.json"
            try:
                with open(playlet_info_path, encoding="utf-8") as f:
                    playlet_info = json.load(f)
            except FileNotFoundError:
                processor.append_to_output(f"Error: {playlet_id}.json not found for playletId {playlet_id}")
                continue

            # 处理视频摘要
            try:
                material_list = playlet_info.get("getPlayletInfo", {}).get("materialList", [])
                summaries = []

                for anchor_video in material_list:
                    video_url = anchor_video.get("videoUrl", "")
                    if video_url:
                        material_id = anchor_video.get("materialId", "")
                        ad_title = anchor_video.get("title", "")

                        logger.info(f"处理视频: {video_url}")

                        try:
                            # 检查缓存中是否已有摘要
                            cached_summary = processor.get_cached_summary(material_id)
                            if cached_summary:
                                logger.info(f"使用缓存摘要: {material_id}")
                                summary = cached_summary
                            elif video_url.startswith("http"):
                                logger.warning(f"跳过网络视频URL: {video_url}")
                                continue
                            else:
                                # 生成新摘要
                                logger.info(f"开始AI分析视频: {video_url}")
                                summary, msg = processor.realize_video(video_url)
                                if summary:
                                    # 保存到缓存
                                    processor.save_summary(playlet_id, material_id, ad_title, video_url, summary)
                                    logger.info(f"摘要已保存到缓存: {material_id}")
                                else:
                                    logger.error(f"AI分析失败: {msg}")
                                    continue

                            if summary and len(summary) > 0:
                                summaries.append({
                                    "playletId": playlet_id,
                                    "videoUrl": video_url,
                                    "materialId": material_id,
                                    "adTitle": ad_title,
                                    "summary": summary
                                })

                        except Exception as e:
                            logger.error(f"处理视频失败 {video_url}: {traceback.format_exc()}")
                            continue

                # 保存摘要并更新到Strapi
                if len(summaries) > 0:
                    with open(playlet_info_path.replace(".json", "_summary.json"), "w", encoding="utf-8") as f:
                        json.dump(summaries, f, ensure_ascii=False, indent=4)

                    data = {
                        "playlet_id": str(playlet_id),
                        "ai_desc": [s["summary"] for s in summaries]
                    }

                    episodes, msg = processor.data_sync.find_episodes(data)
                    processor.append_to_output(msg)

                    if episodes and len(episodes["data"]) > 0:
                        document_id = episodes["data"][0]["documentId"]
                        episodes, msg = processor.data_sync.update_episodes(document_id, data)
                        processor.append_to_output(msg)

                processor.append_to_output(f"Processing data for playletId {playlet_id} has finished.")
                processed += 1

            except Exception as e:
                processor.append_to_output(f"Error processing data for playletId {playlet_id}: {traceback.format_exc()}")

        progress(1.0, desc="AI解析完成")
        processor.append_to_output(f"Summary finished. Total: {total}, Processed: {processed}")
        logger.info(f"{environment}环境视频AI解析完成")
        return f"AI解析完成！总计: {total}, 成功处理: {processed}"

    except Exception as e:
        error_msg = f"AI解析失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

def refresh_logs():
    """刷新日志显示"""
    return logger.get_recent_logs()

def preview_content(file_path):
    """预览文件内容"""
    if not file_path:
        return "请选择文件", gr.update(visible=False), gr.update(visible=False), gr.update(visible=False)
    
    file_extension = os.path.splitext(file_path)[1].lower()
    
    if file_extension in ['.txt', '.json', '.md']:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content, gr.update(visible=True), gr.update(visible=False), gr.update(visible=False)
        except Exception as e:
            return f"读取文件错误: {e}", gr.update(visible=False), gr.update(visible=False), gr.update(visible=False)
    elif file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
        return "", gr.update(visible=False), gr.update(visible=True, value=file_path), gr.update(visible=False)
    elif file_extension in ['.mp4', '.avi', '.mov']:
        return "", gr.update(visible=False), gr.update(visible=False), gr.update(visible=True, value=file_path)
    else:
        return "不支持的文件类型", gr.update(visible=False), gr.update(visible=False), gr.update(visible=False)

def main():
    """创建并启动Gradio界面"""

    with gr.Blocks(title=GRADIO_CONFIG["title"]) as demo:
        gr.Markdown(f"# {GRADIO_CONFIG['title']}")
        
        with gr.Row():
            with gr.Column(scale=1):
                day_input = gr.Textbox(
                    label="日期 (YYYY-MM-DD)", 
                    value=get_yesterday(),
                    placeholder="YYYY-MM-DD"
                )
            with gr.Column(scale=3):
                intersection = gr.TextArea(
                    label="抓包信息", 
                    value=DEFAULT_HEADERS,
                    lines=5
                )
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("### 数据爬取")
                crawl_hot_btn = gr.Button("爬取热力榜", variant="primary")
                crawl_page_btn = gr.Button("爬取详情页")
                
            with gr.Column():
                gr.Markdown("### 线上环境")
                sync_online_btn = gr.Button("同步数据")
                summary_online_btn = gr.Button("AI解析剧情")
            with gr.Column():
                gr.Markdown("### 测试环境")
                summary_test_btn = gr.Button("同步数据")
                sync_test_btn = gr.Button("AI解析剧情")
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("### 文件浏览")
                file_browser = gr.FileExplorer(
                    label="文件浏览器", 
                    root_dir="./data", 
                    file_count="single"
                )
                preview_btn = gr.Button("预览文件")
                
            with gr.Column():
                gr.Markdown("### 文件预览")
                preview_text = gr.Textbox(label="文本预览", lines=20, visible=False)
                preview_image = gr.Image(label="图片预览", visible=False)
                preview_video = gr.Video(label="视频预览", visible=False)
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("### 系统日志")
                refresh_log_btn = gr.Button("刷新日志", variant="secondary")
                log_output = gr.Textbox(
                    label="日志输出", 
                    lines=15, 
                    max_lines=30,
                    interactive=False
                )
        
        # 事件绑定
        crawl_hot_btn.click(
            crawl_hot,
            inputs=[day_input, intersection],
            outputs=[log_output]
        )

        crawl_page_btn.click(
            crawl_detail_pages,
            inputs=[day_input, intersection],
            outputs=[log_output]
        )

        # 线上环境按钮
        sync_online_btn.click(
            lambda day: sync_to_environment(day, "online"),
            inputs=[day_input],
            outputs=[log_output]
        )

        summary_online_btn.click(
            lambda day: summary_videos(day, "online"),
            inputs=[day_input],
            outputs=[log_output]
        )

        # 测试环境按钮 (注意：按钮名称和功能对应)
        summary_test_btn.click(
            lambda day: sync_to_environment(day, "test"),
            inputs=[day_input],
            outputs=[log_output]
        )

        sync_test_btn.click(
            lambda day: summary_videos(day, "test"),
            inputs=[day_input],
            outputs=[log_output]
        )
        
        refresh_log_btn.click(
            refresh_logs,
            outputs=[log_output]
        )
        
        preview_btn.click(
            preview_content,
            inputs=[file_browser],
            outputs=[preview_text, preview_text, preview_image, preview_video]
        )
        
        # 页面加载时显示最新日志
        demo.load(refresh_logs, outputs=[log_output])
    
    demo.launch(
        server_name=GRADIO_CONFIG["server_name"],
        server_port=GRADIO_CONFIG["server_port"]
    )

if __name__ == "__main__":
    main()
