#!/usr/bin/env python3
"""
测试优化后的流程
"""

import sys
import os
import sqlite3
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gradio_app import (
    fetch_api_data, 
    check_entity_exists, 
    save_entity_data, 
    check_summary_exists, 
    save_summary_data,
    BaseProcessor,
    VideoSummaryProcessor
)
from config import DATABASE_NAME

def test_api_simplification():
    """测试API简化"""
    print("=== 测试API简化 ===")
    
    # 模拟API调用（不实际发送请求）
    print("✓ fetch_api_data 现在只返回数据，不返回消息")
    print("✓ 错误处理通过logger记录，不再返回错误消息")

def test_database_operations():
    """测试数据库操作"""
    print("\n=== 测试数据库操作 ===")
    
    try:
        # 测试实体数据操作
        entity_name = "test_image.jpg"
        entity_path = "/path/to/test_image.jpg"
        entity_type = "image"
        playlet_id = "12345"
        
        # 检查不存在的实体
        existing_path = check_entity_exists(entity_name, entity_type)
        if existing_path is None:
            print("✓ check_entity_exists 正确返回 None（实体不存在）")
        
        # 保存实体数据
        success = save_entity_data(entity_name, entity_path, entity_type, playlet_id)
        if success:
            print("✓ save_entity_data 保存成功")
        
        # 检查已存在的实体
        existing_path = check_entity_exists(entity_name, entity_type)
        if existing_path == entity_path:
            print("✓ check_entity_exists 正确返回已存在的路径")
        
        # 测试摘要数据操作
        material_id = "test_material_123"
        summary = "这是一个测试摘要"
        
        # 检查不存在的摘要
        existing_summary = check_summary_exists(material_id)
        if existing_summary is None:
            print("✓ check_summary_exists 正确返回 None（摘要不存在）")
        
        # 保存摘要数据
        success = save_summary_data(playlet_id, material_id, "测试标题", "test_video.mp4", summary)
        if success:
            print("✓ save_summary_data 保存成功")
        
        # 检查已存在的摘要
        existing_summary = check_summary_exists(material_id)
        if existing_summary == summary:
            print("✓ check_summary_exists 正确返回已存在的摘要")
            
    except Exception as e:
        print(f"✗ 数据库操作测试失败: {e}")

def test_processor_caching():
    """测试处理器缓存功能"""
    print("\n=== 测试处理器缓存功能 ===")
    
    try:
        processor = VideoSummaryProcessor("online")
        
        # 测试缓存摘要功能
        material_id = "test_material_123"
        cached_summary = processor.get_cached_summary(material_id)
        
        if cached_summary:
            print("✓ get_cached_summary 正确获取缓存摘要")
        
        # 测试保存摘要功能
        success = processor.save_summary("12345", "new_material_456", "新测试", "new_video.mp4", "新摘要")
        if success:
            print("✓ save_summary 保存摘要成功")
            
    except Exception as e:
        print(f"✗ 处理器缓存测试失败: {e}")

def test_database_schema():
    """测试数据库结构"""
    print("\n=== 测试数据库结构 ===")
    
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        cursor = conn.cursor()
        
        # 检查 entity_data 表结构
        cursor.execute("PRAGMA table_info(entity_data)")
        entity_columns = [row[1] for row in cursor.fetchall()]
        expected_entity_columns = ['id', 'entity_name', 'entity_path', 'entity_type', 'playlet_id', 'created_at']
        
        if all(col in entity_columns for col in expected_entity_columns):
            print("✓ entity_data 表结构正确")
        else:
            print(f"✗ entity_data 表结构不完整: {entity_columns}")
        
        # 检查 drama_summary 表结构
        cursor.execute("PRAGMA table_info(drama_summary)")
        summary_columns = [row[1] for row in cursor.fetchall()]
        expected_summary_columns = ['id', 'playlet_id', 'material_id', 'ad_title', 'video_url', 'summary', 'created_at']
        
        if all(col in summary_columns for col in expected_summary_columns):
            print("✓ drama_summary 表结构正确")
        else:
            print(f"✗ drama_summary 表结构不完整: {summary_columns}")
        
        conn.close()
        
    except Exception as e:
        print(f"✗ 数据库结构测试失败: {e}")

def test_flow_optimization():
    """测试流程优化"""
    print("\n=== 测试流程优化 ===")
    
    optimizations = [
        "✓ API请求只返回数据，不返回消息",
        "✓ 使用ProgressBar显示进度",
        "✓ 使用logger记录日志",
        "✓ 图片和视频下载前检查缓存",
        "✓ AI摘要生成前检查缓存",
        "✓ 媒体文件按playlet_id分目录存储",
        "✓ 数据库记录避免重复下载和分析"
    ]
    
    for optimization in optimizations:
        print(optimization)

def cleanup_test_data():
    """清理测试数据"""
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        cursor = conn.cursor()
        
        # 删除测试数据
        cursor.execute("DELETE FROM entity_data WHERE entity_name LIKE 'test_%'")
        cursor.execute("DELETE FROM drama_summary WHERE material_id LIKE 'test_%' OR material_id LIKE 'new_%'")
        
        conn.commit()
        conn.close()
        print("\n✓ 测试数据清理完成")
        
    except Exception as e:
        print(f"\n✗ 测试数据清理失败: {e}")

def main():
    """运行所有测试"""
    print("开始测试优化后的流程...")
    
    test_api_simplification()
    test_database_operations()
    test_processor_caching()
    test_database_schema()
    test_flow_optimization()
    
    print("\n=== 测试完成 ===")
    print("✓ 表示功能正常")
    print("✗ 表示需要进一步检查")
    
    # 清理测试数据
    cleanup_test_data()

if __name__ == "__main__":
    main()
